spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  profiles:
    active: prod
#    active: dev

# 推流控制配置
stream:
  # 推流总开关，true时允许所有设备推流，false时按观看者逻辑
  tuiliu: false
  # 允许推流的时间段（24小时制）
  allowed-time:
    # 开始时间（小时）
    start: 9
    # 结束时间（小时）
    end: 23

# 使用 mybatis-plus 的配置
#mybatis-plus:
#  type-aliases-package: com.demo.entity
#  configuration:
#    map-underscore-to-camel-case: true
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#  global-config:
#    db-config:
#      id-type: auto
#      logic-delete-field: deleted
#      logic-delete-value: 1
#      logic-not-delete-value: 0
